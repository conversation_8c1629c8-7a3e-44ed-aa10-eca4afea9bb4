import { useState, useRef, useEffect } from 'react';
import { Input } from '@/libs/form/Input';
import { Button } from '@/libs/ui/Button/Button';
import { Icon } from '@/libs/icons/Icon';

type Props = {
  onCreateList: (listName: string) => void;
};

export const CreateNewListButton = ({ onCreateList }: Props) => {
  const [showCreateInput, setShowCreateInput] = useState(false);
  const [newListName, setNewListName] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  const handleCreateNewList = () => {
    setShowCreateInput(true);
  };

  const handleConfirmCreate = () => {
    const trimmedName = newListName.trim();
    if (trimmedName) {
      onCreateList(trimmedName);

      // Reset state
      setShowCreateInput(false);
      setNewListName('');
    }
  };

  const handleCancelCreate = () => {
    setShowCreateInput(false);
    setNewListName('');
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewListName(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleConfirmCreate();
    } else if (e.key === 'Escape') {
      handleCancelCreate();
    }
  };

  // Focus input when it becomes visible
  useEffect(() => {
    if (showCreateInput && inputRef.current) {
      inputRef.current.focus();
    }
  }, [showCreateInput]);

  if (!showCreateInput) {
    return (
      <Button
        onClick={handleCreateNewList}
        variant="white"
        size="sm"
        fullWidth={false}
      >
        <div className="mr-2 rounded-xs border-2 p-[2px]">
          <Icon name="plus" size="0.6rem"></Icon>
        </div>
        Create new list
      </Button>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <Input
        ref={inputRef}
        value={newListName}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        placeholder="Enter list name"
        size="sm"
        className="w-64"
        rightSection={
          <button
            type="button"
            onClick={handleCancelCreate}
            className="flex cursor-pointer items-center justify-center rounded p-1 hover:bg-gray-100"
          >
            <Icon name="multiply" size="0.8rem" color="#666" />
          </button>
        }
      />
      <Button
        onClick={handleConfirmCreate}
        variant="secondary"
        size="sm"
        fullWidth={false}
        disabled={!newListName.trim()}
        className="px-3"
      >
        <Icon name="plus" size="0.8rem" />
      </Button>
    </div>
  );
};
