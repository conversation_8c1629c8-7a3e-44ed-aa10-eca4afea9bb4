import { useState, useRef, useEffect } from 'react';
import { Checkbox } from '@/libs/form/Checkbox';
import { Input } from '@/libs/form/Input';
import { Button } from '@/libs/ui/Button/Button';
import { CustomList } from './components/CustomList/CustomList';
import { ProductType } from '@/types';
import { useListFiltering } from './hooks/useListFiltering';
import mockData from './mockData.json';

import { Icon } from '@/libs/icons/Icon';

type Props = {
  setActiveTab: (tab: number) => void;
};

const productsData: ProductType[] = mockData.productsData.map((product) => ({
  ...product,
  offers: product.offers.map((offer) => ({
    ...offer,
    vendor: {
      ...offer.vendor,
      type: offer.vendor.type as 'manufacturer' | 'distributor',
    },
    stockStatus: offer.stockStatus as 'IN_STOCK' | 'BACKORDER' | 'OUT_OF_STOCK',
    product: null as unknown as ProductType,
  })),
}));
productsData.forEach((product) => {
  product.offers.forEach((offer) => {
    offer.product = product;
  });
});

const mockCustomLists = mockData.mockCustomLists.map((list) => ({
  name: list.name,
  createdBy: 'Lima Neto',
  lastUpdated: '02/02/2025',
  products: list.productIds.map(
    (id) => productsData.find((product) => product.id === id)!,
  ),
}));

export const CustomLists = ({ setActiveTab }: Props) => {
  const { openModal } = useModalStore.getState();
  const [searchQuery, setSearchQuery] = useState('');
  const [showCreateInput, setShowCreateInput] = useState(false);
  const [newListName, setNewListName] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  const { filteredLists } = useListFiltering({
    lists: mockCustomLists,
    searchQuery,
  });

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
  };

  const handleCreateNewList = () => {
    setShowCreateInput(true);
  };

  const handleConfirmCreate = () => {
    const trimmedName = newListName.trim();
    if (trimmedName) {
      // TODO: Add logic to actually create the list
      console.log('Creating new list:', trimmedName);

      // Reset state
      setShowCreateInput(false);
      setNewListName('');
    }
  };

  const handleCancelCreate = () => {
    setShowCreateInput(false);
    setNewListName('');
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewListName(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleConfirmCreate();
    } else if (e.key === 'Escape') {
      handleCancelCreate();
    }
  };

  // Focus input when it becomes visible
  useEffect(() => {
    if (showCreateInput && inputRef.current) {
      inputRef.current.focus();
    }
  }, [showCreateInput]);

  return (
    <div>
      <div className="mt-4 mb-6 flex items-center justify-between">
        <Checkbox
          checked={false}
          onChange={() => {}}
          label="Select all your lists"
        />
        <div className="flex items-center gap-4">
          <form onSubmit={handleSearchSubmit}>
            <Input
              placeholder="Search lists or item"
              value={searchQuery}
              onChange={(e) =>
                setSearchQuery(e.target.value.toLocaleLowerCase())
              }
              size="sm"
              className="w-64"
            />
          </form>
          {!showCreateInput ? (
            <Button
              onClick={handleCreateNewList}
              variant="white"
              size="sm"
              fullWidth={false}
            >
              <div className="mr-2 rounded-xs border-2 p-[2px]">
                <Icon name="plus" size="0.6rem"></Icon>
              </div>
              Create new list
            </Button>
          ) : (
            <div className="flex items-center gap-2">
              <Input
                ref={inputRef}
                value={newListName}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                placeholder="Enter list name"
                size="sm"
                className="w-64"
              />
              <Button
                onClick={handleConfirmCreate}
                variant="secondary"
                size="sm"
                fullWidth={false}
                disabled={!newListName.trim()}
                className="px-3"
              >
                <Icon name="plus" size="0.8rem" />
              </Button>
              <Button
                onClick={handleCancelCreate}
                variant="white"
                size="sm"
                fullWidth={false}
                className="px-3"
              >
                <Icon name="multiply" size="0.8rem" />
              </Button>
            </div>
          )}
        </div>
      </div>
      <div className="space-y-4">
        {filteredLists
          .filter((list) => list.isVisible)
          .map((list, index) => (
            <CustomList
              key={`${list.name}-${index}`}
              setActiveTab={setActiveTab}
              list={list}
              searchQuery={searchQuery}
            />
          ))}
      </div>
    </div>
  );
};
